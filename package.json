{"name": "tinyimage", "version": "1.0.0", "private": true, "description": "A modern, fast, and user-friendly image compression tool built with Next.js", "keywords": ["image compression", "optimize images", "reduce file size", "JPEG", "PNG", "WebP", "batch processing"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/chirag127"}, "repository": {"type": "git", "url": "https://github.com/chirag127/TinyImage.git"}, "bugs": {"url": "https://github.com/chirag127/TinyImage/issues"}, "homepage": "https://github.com/chirag127/TinyImage#readme", "license": "MIT", "scripts": {"dev": "next dev --turbopack", "build": "npm run generate-assets && next build", "start": "next start", "lint": "next lint", "generate-assets": "node scripts/generate-pngs.js"}, "dependencies": {"@types/react-dropzone": "^4.2.2", "lucide-react": "^0.525.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "sharp": "^0.34.3", "tailwindcss": "^4", "typescript": "^5"}}